"""
邢不行™️ 策略分享会
仓位管理框架

版权所有 ©️ 邢不行
微信: xbx6660

本代码仅供个人学习使用，未经授权不得复制、修改或用于商业用途。

Author: 邢不行

动量因子1的分位数排名版本
基于原始动量因子1，增加时序分位数排名处理，提高因子稳定性和时间一致性
"""


def signal(*args):
    """
    动量因子1分位数版本
    
    计算逻辑：
    1. 计算n周期内的最高价和最低价
    2. 计算原始动量值：(收盘价 - 最低价) / (最高价 - 最低价)
    3. 对原始动量值进行n周期时序分位数排名
    
    参数：
    - args[0]: DataFrame，包含OHLC数据
    - args[1]: int，计算周期n
    - args[2]: str，因子名称
    
    返回：
    - DataFrame，包含分位数排名后的动量因子
    """
    df = args[0]
    n = args[1]
    factor_name = args[2]
    
    # 保存原始列名，用于最后清理中间变量
    source_cls = df.columns.tolist()
    
    # 第一步：计算n周期内的最高价和最低价
    df['max_high'] = df['high'].rolling(n, min_periods=1).max()
    df['min_low'] = df['low'].rolling(n, min_periods=1).min()
    
    # 第二步：计算原始动量值，添加除零保护
    denominator = df['max_high'] - df['min_low']
    # 防止除零：当最高价等于最低价时，设置为极小值
    denominator = denominator.replace(0, 1e-8)
    
    df['raw_momentum'] = (df['close'] - df['min_low']) / denominator
    
    # 第三步：对原始动量值进行时序分位数排名
    # ascending=True: 最小值排名为0，最大值排名为1
    # pct=True: 返回分位数排名(0-1之间)，而不是序号排名
    df[factor_name] = df['raw_momentum'].rolling(n, min_periods=1).rank(ascending=True, pct=True)
    
    # 清理中间变量，只保留原始列和新因子列
    columns_to_drop = list(set(df.columns.values).difference(set(source_cls + [factor_name])))
    df.drop(columns=columns_to_drop, inplace=True)
    
    return df


def signal_multi_params(df, param_list) -> dict:
    """
    多参数批量计算版本，提升计算效率
    
    参数：
    - df: DataFrame，包含OHLC数据
    - param_list: list，参数列表
    
    返回：
    - dict，键为参数字符串，值为对应的因子序列
    """
    ret = dict()
    
    for param in param_list:
        n = int(param)
        
        # 计算n周期内的最高价和最低价
        max_high = df['high'].rolling(n, min_periods=1).max()
        min_low = df['low'].rolling(n, min_periods=1).min()
        
        # 计算原始动量值，添加除零保护
        denominator = (max_high - min_low).replace(0, 1e-8)
        raw_momentum = (df['close'] - min_low) / denominator
        
        # 时序分位数排名
        factor_values = raw_momentum.rolling(n, min_periods=1).rank(ascending=True, pct=True)
        
        ret[str(param)] = factor_values
    
    return ret


# 因子说明和使用建议
"""
因子特征：
1. 输出范围：[0, 1]，0表示在历史n周期中动量最低，1表示动量最高
2. 时间一致性：通过分位数排名确保不同时期的因子值具有可比性
3. 异常值处理：分位数排名自动处理极端值，提高稳定性
4. 趋势识别：识别价格在自身历史中的相对位置变化

使用建议：
1. 参数选择：建议使用较长周期(如1000-5000)以捕捉长期趋势
2. 排序方式：
   - False(降序)：选择分位数较低的币种，适合反转策略
   - True(升序)：选择分位数较高的币种，适合动量策略
3. 组合使用：可与流动性因子、波动率因子等组合使用

与原始动量因子1的区别：
1. 原始版本：直接输出(收盘价-最低价)/(最高价-最低价)
2. 分位数版本：对原始值进行时序排名，提高时间稳定性
3. 适用场景：分位数版本更适合长期策略和多因子组合
"""
