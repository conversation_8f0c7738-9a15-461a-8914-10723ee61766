# 邢不行® 策略分享会️

## 🗃️ 动态仓位管理回测框架

✨ 全新的仓位管理策略框架

- 🔮 支持动态仓位管理，
- ⌚ 支持择时策略，
- 🔀 具备多类型、多周期策略动态融合功能，
- 🪄 兼容所有选币策略，并提供灵活高效的多策略配置


### v1.3.5

> 发布时间：2025-07-22

- 单币权重限制功能扩展
  - 对多头/空头支持使用选币为空的策略进行填充，让超过限制的仓位进行空仓


#### ℹ️ 更新说明

- [backtest_config.py](core/model/backtest_config.py)


---

### v1.3.4

> 发布时间：2025-07-14

- 增加单币权重限制功能
  - 该功能作用于账户级别，在所有策略全部选币完成之后，可以分别针对多头和空头单币权重设置阈值，超过部分，可以设置使用其他策略填充
- 资金曲线增加每小时单币持仓权重最大top3币种显示


#### ℹ️ 更新说明

- [core](core)
- [backtest.py](backtest.py)


---


### v1.3.3

> 发布时间：2025-06-26

- 策略配置增加币种数量限制
    - long_select_coin_num_max（多头选币最大数量，实际会选择 min(long_select_coin_num, long_select_coin_num_max)，保证多头选币不会太多，单币权重过低）
    - short_select_coin_num_min（空头选币最小数量，实际会选择 max(short_select_coin_num, short_select_coin_num_min)，保证空投选币不会过低，单币权重过大）
    - 可以设置选币最大数量和最小数量。 
    - 比如你设置30%的选币范围，同时又设置了最大选币数量50。 
    - 那实际的选币数是min(30%选币，50)。 
    - 比如你设置1%的选币范围，同时又设置了最小选币数量3。 
    - 那实际的选币数是max(1%选币，3)。


#### ℹ️ 更新说明

- [strategy_config.py](core/model/strategy_config.py)
- [strategy_hub.py](core/utils/strategy_hub.py)
- [select_coin.py](core/select_coin.py)


---

### v1.3.2

> 发布时间：2025-06-13

- 新增`tools`目录，增加参数分析工具生成参数平原/热力图
  - 基于选币框架中工具进行调整，大体上使用方式一致
- 新增带 offset 轮动策略文件
  - 之前轮动是只看子策略1h资金曲线，只有一个offset，存在频繁轮动的情况
  - 带 offset 轮动策略文件，支持子策略任何周期的资金曲线，同时支持offset覆盖，降低轮动切换频率，让轮动更加稳定


#### 修改文件

- [core/model/backtest_config.py](core/model/backtest_config.py)
- [core/figure.py](core/figure.py)
- [positions/RotationStrategyOffset.py](positions/RotationStrategyOffset.py)
- [tools](tools)
- [param_search_beta.py](param_search_beta.py)



---

### v1.3.1

> 发布时间：2025-05-21

- 新增 market 配置(如果一下配置没有看懂，可以看一下@学费菌老板的帖子)
  - 选币市场范围 & 交易配置
  - 配置解释： 选币范围 + '_' + 优先交易币种类型
  - spot_spot: 在 '现货' 市场中进行选币。如果现货币种含有'合约'，优先交易 '现货'。
  - swap_swap: 在 '合约' 市场中进行选币。如果现货币种含有'现货'，优先交易 '合约'。
  - spot_swap: 在 '现货' 市场中进行选币。如果现货币种含有'合约',优先交易'合约'。
  - mix_spot:  在 '现货与合约' 的市场中进行选币。如果两边市场都存在的币种，会保留'现货'，并优先下单'现货'。
  - mix_swap:  在 '现货与合约' 的市场中进行选币。如果两边市场都存在的币种，会保留'合约'，并优先下单'合约'。
- 优化计算截面因子的内存
- 1.3.0的截面因子写法已经被抛弃(注意之前 config 配置现在迁移到截面因子内，参考`sections`案例因子)
- 移除配置可以控制现货中不含有合约，合约中不含有现货等设置。(建议通过因子来进行控制)
- 增加遍历脚本Beta版本(存在一个已知问题，运行时注意查看提示)


#### 修改文件

- [core](core)
- [sections](sections)


---

### v1.3.0

> 发布时间：2025-04-28

- 回测增加多空选币数量 & 多空仓位比例
- 增加截面因子计算
- 增加配置项：现货模式中是否包含合约的币，合约模式中是包含现货的币

#### 修改文件

- [sections](sections)
- [strategy_config.py](core/model/strategy_config.py)
- [factor_hub.py](core/utils/factor_hub.py)
- [backtest.py](core/backtest.py)
- [equity.py](core/equity.py)
- [figure.py](core/figure.py)
- [select_coin.py](core/select_coin.py)

---

### v1.2.2

🔧 解决部分情况下计算目标手数数据溢出的问题

> 发布时间: 2025-02-28

#### 更新内容

- [`core/rebalance.py`](core/rebalance.py)

---

### v1.2.1

🔧 解决部分情况下再择时杠杆对不齐的情况

> 发布时间: 2025-01-21

#### 更新内容

- 🔧 解决部分情况下再择时杠杆对不齐的情况


#### 更新内容

- [`core/backtest.py`](core/backtest.py)
- [`core/version.py`](core/version.py)
- [`core/model/backtest_config.py`](core/model/backtest_config.py)
- [`config.py`](config.py)
- 本更新文件

-----

### v1.2.0

> 发布时间: 2025-01-11

#### 更新内容

- 新增外部数据的支持
