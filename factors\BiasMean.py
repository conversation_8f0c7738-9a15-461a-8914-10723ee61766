"""
邢不行™️ 策略分享会
仓位管理框架

版权所有 ©️ 邢不行
微信: xbx1717

本代码仅供个人学习使用，未经授权不得复制、修改或用于商业用途。

Author: 邢不行

# ** 因子文件功能说明 **
1. 因子库中的每个 Python 文件需实现 `signal` 函数，用于计算因子值。
2. 除 `signal` 外，可根据需求添加辅助函数，不影响因子计算逻辑。

# ** signal 函数参数与返回值说明 **
1. `signal` 函数的第一个参数为 `candle_df`，用于接收单个币种的 K 线数据。
2. `signal` 函数的第二个参数用于因子计算的主要参数，具体用法见函数实现。
3. `signal` 函数可以接收其他可选参数，按实际因子计算逻辑使用。
4. `signal` 函数的返回值应为包含因子数据的 K 线数据。

# ** BiasMean 因子说明 **
BiasMean 因子计算价格相对于移动平均线的偏离度的平均值：
1. 计算收盘价相对于 n 周期移动平均线的偏离度：(close / ma - 1)
2. 对偏离度进行 n 周期的移动平均，得到平均偏离度
3. 正值表示价格持续高于均线，负值表示价格持续低于均线
4. 该因子可用于识别趋势的持续性和强度

# ** 使用示例 **
- 如果策略配置中 `factor_list` 包含 ('BiasMean', False, 20, 1)，则计算 20 周期的 BiasMean 因子
- 参数 n 通常设置为 10、20、50、100 等，根据策略周期选择
"""

import pandas as pd


def signal(*args) -> pd.DataFrame:
    """
    BiasMean 因子计算函数

    参数:
    - df: K线数据，至少包含列 ['close']
    - n: 计算周期参数
    - factor_name: 输出列名（框架会按 {name}_{param} 组织）

    计算逻辑:
    1. 计算 n 周期移动平均线：ma = close.rolling(n).mean()
    2. 计算偏离度：bias = close / ma - 1
    3. 计算偏离度的 n 周期移动平均：bias_mean = bias.rolling(n).mean()

    返回:
    - 在 df 上新增一列 factor_name，包含 BiasMean 因子值
    """
    df = args[0]
    n = int(args[1])
    factor_name = args[2]

    # 计算移动平均线
    ma = df['close'].rolling(n, min_periods=1).mean()

    # 计算偏离度
    bias = df['close'] / ma - 1

    # 计算偏离度的移动平均
    df[factor_name] = bias.rolling(n, min_periods=1).mean()

    return df


def signal_multi_params(df: pd.DataFrame, param_list) -> dict:
    """
    多参数加速版本：一次计算多个周期的 BiasMean 因子

    参数:
    - df: K线数据
    - param_list: 参数列表，如 [10, 20, 50]

    返回:
    - dict[str(param)] = pd.Series，包含各个周期的 BiasMean 因子值
    """
    ret = {}
    close = df['close']

    for param in param_list:
        n = int(param)

        # 计算移动平均线
        ma = close.rolling(n, min_periods=1).mean()

        # 计算偏离度
        bias = close / ma - 1

        # 计算偏离度的移动平均
        bias_mean = bias.rolling(n, min_periods=1).mean()

        ret[str(param)] = bias_mean

    return ret
