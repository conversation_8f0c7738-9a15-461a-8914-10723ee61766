"""
邢不行™️ 策略分享会
仓位管理框架

版权所有 ©️ 邢不行
微信: xbx6660

本代码仅供个人学习使用，未经授权不得复制、修改或用于商业用途。

Author: 邢不行
"""


def signal(*args):
    """
    布林带突破后置过滤因子
    
    参数说明:
    - df: 数据框
    - n: 布林带周期参数
    - factor_name: 因子名称
    
    计算逻辑:
    1. 计算n周期的移动平均线
    2. 计算n周期的标准差
    3. 计算布林带上下轨
    4. 计算当前价格相对于布林带的位置
    5. 返回突破信号值
    
    返回值:
    - 正值表示突破上轨（看涨信号）
    - 负值表示突破下轨（看跌信号）
    - 0附近表示在布林带内震荡
    """
    df = args[0]
    n = args[1]
    factor_name = args[2]

    # 计算移动平均线
    df['ma'] = df['close'].rolling(window=n, min_periods=1).mean()
    
    # 计算标准差
    df['std'] = df['close'].rolling(window=n, min_periods=1).std()
    
    # 计算布林带上下轨
    df['upper'] = df['ma'] + 2 * df['std']  # 上轨：MA + 2*标准差
    df['lower'] = df['ma'] - 2 * df['std']  # 下轨：MA - 2*标准差
    
    # 计算价格相对于布林带的位置
    # 当价格突破上轨时，值为正数；突破下轨时，值为负数
    # 在布林带内时，值接近0
    df[factor_name] = (df['close'] - df['ma']) / (df['std'] + 1e-8)  # 加1e-8避免除零
    
    return df
