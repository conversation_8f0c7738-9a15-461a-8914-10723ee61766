"""
邢不行™️ 策略分享会
选币策略框架𝓟𝓻𝓸

版权所有 ©️ 邢不行
微信: xbx1717

本代码仅供个人学习使用，未经授权不得复制、修改或用于商业用途。

Author: 邢不行
"""

import pandas as pd


def signal(*args) -> pd.DataFrame:
    """
    RSI（相对强弱指数）后置过滤因子

    参数:
    - df: K线数据，至少包含列 ['close']
    - n: RSI 周期参数（常用 14、21、30 等）
    - factor_name: 输出列名（框架会按 {name}_{param} 组织）

    计算逻辑:
    1. 计算收盘价的变化量 delta
    2. 将正向变化记为 gain，负向变化的绝对值记为 loss
    3. 计算 n 周期的平均 gain 与平均 loss（使用简单移动平均，min_periods=1 以便前期不缺失）
    4. RS = avg_gain / (avg_loss + 1e-12)
    5. RSI = 100 - 100 / (1 + RS)

    返回:
    - 在 df 上新增一列 factor_name，取值范围 [0, 100]
      较高数值表示相对更强势，较低数值表示相对更弱势
    """
    df = args[0]
    n = int(args[1])
    factor_name = args[2]

    close = df['close']
    delta = close.diff()
    gain = delta.where(delta > 0, 0.0)
    loss = (-delta).where(delta < 0, 0.0)

    avg_gain = gain.rolling(window=n, min_periods=1).mean()
    avg_loss = loss.rolling(window=n, min_periods=1).mean()

    rs = avg_gain / (avg_loss + 1e-12)
    rsi = 100.0 - (100.0 / (1.0 + rs))

    df[factor_name] = rsi
    return df


def signal_multi_params(df: pd.DataFrame, param_list) -> dict:
    """
    多参数加速版本：一次遍历 close/delta，按不同周期 n 输出多个 RSI 序列

    返回:
    - dict[str(param)] = pd.Series
    """
    close = df['close']
    delta = close.diff()
    gain = delta.where(delta > 0, 0.0)
    loss = (-delta).where(delta < 0, 0.0)

    ret = {}
    for param in param_list:
        n = int(param)
        avg_gain = gain.rolling(window=n, min_periods=1).mean()
        avg_loss = loss.rolling(window=n, min_periods=1).mean()
        rs = avg_gain / (avg_loss + 1e-12)
        rsi = 100.0 - (100.0 / (1.0 + rs))
        ret[str(param)] = rsi
    return ret

